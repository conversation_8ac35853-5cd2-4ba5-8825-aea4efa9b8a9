import { liveStreamingApi } from '../../../../redux/api/api';

const initialState = {
  studentLiveStreamingData: null,
  activeStreams: [],
  currentStream: null,
  chatMessages: [],
  chatLoading: false,
  chatError: null
};

export const studentLiveStreamingSlice = liveStreamingApi.injectEndpoints({
  endpoints: (builder) => ({
    getActiveStreamsForStudent: builder.query({
      query: () => ({
        url: '/active-streams',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Student Active Streams Data:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentLiveStreaming']
    }),

    joinStreamAsStudent: builder.mutation({
      query: (sessionData) => {
        // Get user data from sessionStorage
        const userId = sessionStorage.getItem('userId');
        const userName = sessionStorage.getItem('name') || 'Student';

        // Prepare the request body with required fields for student
        const requestBody = {
          session_id: sessionData.session_id,
          user_id: userId,
          user_name: userName,
          user_role: "student"
        };

        console.log('Student Join Stream Request Body:', requestBody);

        return {
          url: '/api/livekit/join',
          method: 'POST',
          body: requestBody,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Student Join Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['StudentLiveStreaming']
    }),

    // Chat functionality for students
    sendStudentChatMessage: builder.mutation({
      query: (messageData) => {
        console.log('Student Send Chat Message Request:', messageData);

        return {
          url: '/api/chat/send',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionStorage.getItem('token')}`
          },
          body: messageData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Student Send Chat Message Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),

    getStudentChatHistory: builder.query({
      query: (sessionId) => {
        console.log('Student Get Chat History for session:', sessionId);

        return {
          url: `/api/chat/history/${sessionId}`,
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${sessionStorage.getItem('token')}`
          },
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Student Chat History Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    })
  })
});

export const {
  useGetActiveStreamsForStudentQuery,
  useLazyGetActiveStreamsForStudentQuery,
  useJoinStreamAsStudentMutation,
  useSendStudentChatMessageMutation,
  useLazyGetStudentChatHistoryQuery
} = studentLiveStreamingSlice;

// Slice for local state management
const studentLiveStreamingStateSlice = {
  name: 'studentLiveStreaming',
  initialState,
  reducers: {
    setCurrentStream: (state, action) => {
      state.currentStream = action.payload;
    },
    clearCurrentStream: (state) => {
      state.currentStream = null;
    },
    setChatMessages: (state, action) => {
      state.chatMessages = action.payload;
    },
    addChatMessage: (state, action) => {
      state.chatMessages.push(action.payload);
    },
    setChatLoading: (state, action) => {
      state.chatLoading = action.payload;
    },
    setChatError: (state, action) => {
      state.chatError = action.payload;
    },
    clearChatMessages: (state) => {
      state.chatMessages = [];
    }
  }
};

export const {
  setCurrentStream,
  clearCurrentStream,
  setChatMessages,
  addChatMessage,
  setChatLoading,
  setChatError,
  clearChatMessages
} = studentLiveStreamingStateSlice.actions;

export default studentLiveStreamingStateSlice.reducer;
