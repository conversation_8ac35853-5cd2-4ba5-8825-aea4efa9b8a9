import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router';
import SideBar from './SideBar';
import { motion } from 'framer-motion';
import TopBar from './TopBar';
import ChatBotSystem from '../../pages/screens/studentPanel/chatSupport/ChatBotSystem';
import Button from '../Field/Button';
import { ArrowLeftSquare } from 'lucide-react';

const Navbar = ({ children }) => {
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const [showNav, setShowNav] = useState(true);
  const userRole = sessionStorage.getItem('role');
  const location = useLocation();

  const hiddenNavPaths = [
    '/sasthra/student/mock-test-simulation',
    '/sasthra/student/create-your-own-test',
    '/sasthra/student/ai-tutor',
    '/sasthra/teacher/live-streaming',
    '/sasthra/faculty/live-viewer',
    '/sasthra/student/problem-solver',
    '/sasthra/student/learn-practically',
    '/sasthra/student/student-community',
  ];

  const isHiddenNavPage = hiddenNavPaths.includes(location.pathname);

  useEffect(() => {
    if (isHiddenNavPage) {
      setShowNav(true);
    }
  }, [location.pathname]);

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };


  return (
    <div className="h-screen flex flex-col bg-[#f9fafb]">
      {!isHiddenNavPage || !showNav ? <TopBar onToggleSidebar={toggleSidebar} /> : null}

      <div className="flex flex-grow overflow-hidden">
        {!isHiddenNavPage || !showNav ? <SideBar isOpen={isSidebarOpen} /> : null}

        <div className="flex flex-col flex-grow overflow-y-auto">
          <main className="flex-grow">
            {/* {isHiddenNavPage && showNav && (
              <motion.button
                onClick={() => setShowNav(false)}
                className="relative p-2 group"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}>
                <motion.div
                  className="absolute inset-0 bg-blue-500/10 rounded-lg"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  whileHover={{ scale: 1.2, opacity: 0.3 }}
                />

                <motion.div className="flex items-center  justify-center" whileHover={{ x: -3 }}>
                  <ArrowLeftSquare
                    size={24}
                    className="text-blue-600 group-hover:text-blue-700 transition-colors"
                  />
                  <motion.span
                    className="ml-2  text-sm font-medium hover:cursor-pointer text-blue-600 group-hover:text-blue-700"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}>
                    Back...
                  </motion.span>
                </motion.div>
              </motion.button>
            )} */}
            {children}
          </main>

          {userRole === 'student' && (
            <div className="fixed bottom-16 right-4 z-10">
              {!isHiddenNavPage || !showNav ? <ChatBotSystem /> : null}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Navbar;
